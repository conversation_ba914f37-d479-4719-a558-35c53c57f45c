/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'unplugin-vue-router/types'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    'root': RouteRecordInfo<'root', '/', Record<never, never>, Record<never, never>>,
    '$error': RouteRecordInfo<'$error', '/:error(.*)', { error: ParamValue<true> }, { error: ParamValue<false> }>,
    'admin-users': RouteRecordInfo<'admin-users', '/admin/users', Record<never, never>, Record<never, never>>,
    'api-docs': RouteRecordInfo<'api-docs', '/api-docs', Record<never, never>, Record<never, never>>,
    'api-key': RouteRecordInfo<'api-key', '/api-key', Record<never, never>, Record<never, never>>,
    'dashboard': RouteRecordInfo<'dashboard', '/dashboard', Record<never, never>, Record<never, never>>,
    'flutterwave': RouteRecordInfo<'flutterwave', '/flutterwave', Record<never, never>, Record<never, never>>,
    'flutterwave-payment-redirect': RouteRecordInfo<'flutterwave-payment-redirect', '/flutterwave/payment-redirect', Record<never, never>, Record<never, never>>,
    'flutterwave-redirect-results': RouteRecordInfo<'flutterwave-redirect-results', '/flutterwave/redirect-results', Record<never, never>, Record<never, never>>,
    'korapay': RouteRecordInfo<'korapay', '/korapay', Record<never, never>, Record<never, never>>,
    'korapay-payment-redirect': RouteRecordInfo<'korapay-payment-redirect', '/korapay/payment-redirect', Record<never, never>, Record<never, never>>,
    'korapay-redirect-results': RouteRecordInfo<'korapay-redirect-results', '/korapay/redirect-results', Record<never, never>, Record<never, never>>,
    'login': RouteRecordInfo<'login', '/login', Record<never, never>, Record<never, never>>,
    'neon-pay-api-docs': RouteRecordInfo<'neon-pay-api-docs', '/neon-pay/api-docs', Record<never, never>, Record<never, never>>,
    'payer-gateway-api-docs': RouteRecordInfo<'payer-gateway-api-docs', '/payer-gateway/api-docs', Record<never, never>, Record<never, never>>,
    'payment-redirect': RouteRecordInfo<'payment-redirect', '/payment-redirect', Record<never, never>, Record<never, never>>,
    'profile': RouteRecordInfo<'profile', '/profile', Record<never, never>, Record<never, never>>,
    'redirect-results': RouteRecordInfo<'redirect-results', '/redirect-results', Record<never, never>, Record<never, never>>,
    'register': RouteRecordInfo<'register', '/register', Record<never, never>, Record<never, never>>,
    'second-page': RouteRecordInfo<'second-page', '/second-page', Record<never, never>, Record<never, never>>,
    'transactions': RouteRecordInfo<'transactions', '/transactions', Record<never, never>, Record<never, never>>,
  }
}
