<script setup>
import { onMounted, ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useKorapayStore } from '@/stores/korapay'

const route = useRoute()
const router = useRouter()
const korapayStore = useKorapayStore()

const txRef = route.query.tx_ref
const status = route.query.status

const message = ref('Processing Korapay payment...')
const verification = ref(null)
const isLoading = ref(true)
const pollingInterval = 5000
const maxPollTime = 60000 // 1 minute
let pollTimer = null
let elapsed = 0

const isSuccess = computed(() => status === 'successful' && verification.value)
const isError = computed(() => status !== 'successful' || (status === 'successful' && !verification.value))

async function verifyPayment() {
  try {
    const res = await korapayStore.verifyPayment(txRef)
    verification.value = res

    // Update message or stop polling depending on payment status
    if (verification.value.status === 'successful' || verification.value.status === 'failed') {
      message.value = verification.value.status === 'successful'
        ? 'Korapay payment completed successfully!'
        : 'Korapay payment failed. Please try again.'
      isLoading.value = false
      clearInterval(pollTimer)
    } else {
      // Still processing
      message.value = 'Korapay payment is still processing, please wait...'
    }
  } catch (e) {
    console.error('Korapay verification failed:', e)
    message.value = 'Korapay payment verification failed. Please contact support.'
    isLoading.value = false
    clearInterval(pollTimer)
  }
}

onMounted(() => {
  if (status === 'successful') {
    verifyPayment() // initial call

    pollTimer = setInterval(() => {
      elapsed += pollingInterval
      if (elapsed >= maxPollTime) {
        clearInterval(pollTimer)
        message.value = 'Verification timed out. Please check your transaction later.'
        isLoading.value = false
        
        return
      }
      verifyPayment()
    }, pollingInterval)
  } else {
    message.value = 'Korapay payment was cancelled or failed.'
    isLoading.value = false
  }
})

const goHome = () => router.push('/korapay')
const tryAgain = () => router.push('/korapay')
</script>

<template>
  <div class="payment-result-page">
    <!-- Loading State -->
    <div
      v-if="isLoading"
      class="text-center"
    >
      <VProgressCircular
        indeterminate
        size="64"
        color="primary"
        class="mb-4"
      />
      <h2 class="text-h5 mb-2">
        Processing Korapay Payment...
      </h2>
      <p class="text-body-1 text-medium-emphasis">
        Please wait while we verify your Korapay payment
      </p>
    </div>

    <!-- Success State -->
    <VCard
      v-else-if="isSuccess"
      class="mx-auto result-card"
      max-width="600"
      elevation="8"
    >
      <VCardText class="text-center pa-8">
        <VIcon
          icon="ri-checkbox-circle-line"
          size="80"
          color="success"
          class="mb-4"
        />
        <h1 class="text-h4 font-weight-bold mb-4 text-success">
          Payment Successful!
        </h1>
        <p class="text-body-1 mb-6">
          {{ message }}
        </p>

        <!-- Payment Details -->
        <VCard
          v-if="verification"
          variant="tonal"
          color="success"
          class="mb-6"
        >
          <VCardTitle class="text-center">
            <VIcon
              icon="ri-file-list-3-line"
              class="me-2"
            />
            Korapay Payment Details
          </VCardTitle>
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="6"
              >
                <div class="text-center">
                  <p class="text-caption text-medium-emphasis mb-1">
                    Transaction Reference
                  </p>
                  <p class="text-body-1 font-weight-medium">
                    {{ txRef }}
                  </p>
                </div>
              </VCol>
              <VCol
                cols="12"
                sm="6"
              >
                <div class="text-center">
                  <p class="text-caption text-medium-emphasis mb-1">
                    Status
                  </p>
                  <VChip
                    color="success"
                    size="small"
                  >
                    {{ verification.status || 'Completed' }}
                  </VChip>
                </div>
              </VCol>
              <VCol
                v-if="verification.amount"
                cols="12"
                sm="6"
              >
                <div class="text-center">
                  <p class="text-caption text-medium-emphasis mb-1">
                    Amount
                  </p>
                  <p class="text-body-1 font-weight-medium">
                    {{ verification.amount }} {{ verification.currency }}
                  </p>
                </div>
              </VCol>
              <VCol
                cols="12"
                sm="6"
              >
                <div class="text-center">
                  <p class="text-caption text-medium-emphasis mb-1">
                    Provider
                  </p>
                  <VChip
                    color="primary"
                    size="small"
                  >
                    Korapay
                  </VChip>
                </div>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>

        <VBtn
          color="primary"
          size="large"
          @click="goHome"
        >
          <VIcon
            icon="ri-home-line"
            class="me-2"
          />
          Back to Korapay
        </VBtn>
      </VCardText>
    </VCard>

    <!-- Error State -->
    <VCard
      v-else
      class="mx-auto result-card"
      max-width="600"
      elevation="8"
    >
      <VCardText class="text-center pa-8">
        <VIcon
          icon="ri-close-circle-line"
          size="80"
          color="error"
          class="mb-4"
        />
        <h1 class="text-h4 font-weight-bold mb-4 text-error">
          Payment Failed
        </h1>
        <p class="text-body-1 mb-6">
          {{ message }}
        </p>

        <div class="d-flex flex-column flex-sm-row gap-4 justify-center">
          <VBtn
            color="primary"
            variant="outlined"
            @click="tryAgain"
          >
            <VIcon
              icon="ri-refresh-line"
              class="me-2"
            />
            Try Again
          </VBtn>
          <VBtn
            color="primary"
            @click="goHome"
          >
            <VIcon
              icon="ri-home-line"
              class="me-2"
            />
            Back to Korapay
          </VBtn>
        </div>
      </VCardText>
    </VCard>
  </div>
</template>

<style scoped>
.payment-result-page {
  padding: 2rem 1rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-card {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.v-btn {
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
}

@media (max-width: 768px) {
  .payment-result-page {
    padding: 1rem 0.5rem;
  }

  .result-card {
    margin: 0 !important;
  }
}
</style>
