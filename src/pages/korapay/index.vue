<script setup>
import { ref, computed, watch } from 'vue'
import { useKorapayStore } from '@/stores/korapay'

const router = useRouter()
const korapayStore = useKorapayStore()

// Form data
const phone = ref('0547099292')
const amount = ref('1000')
const fullname = ref('<PERSON>')
const email = ref('<EMAIL>')
const txRef = ref('KRP-' + Date.now())
const paymentType = ref('mobile_money_ghana')
const country = ref('GH')
const network = ref('MTN')
const order_ref = ref('korapay-order-' + Date.now())

// UI state
const isLoading = ref(false)
const formErrors = ref({})

// Generate transaction reference automatically
const generateTxRef = () => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  
  return `KRP-${timestamp}-${random}`.toUpperCase()
}

// Korapay payment type configurations
const paymentConfigs = {
  'mobile_money_ghana': { country: 'GH', networks: ['MTN', 'Vodafone', 'AirtelTigo'], currency: 'GHS' },
  'mobile_money_kenya': { country: 'KE', networks: ['Safaricom', 'Airtel'], currency: 'KES' },
  'mobile_money_nigeria': { country: 'NG', networks: ['MTN', 'Airtel', 'Glo', '9mobile'], currency: 'NGN' },
  'bank_transfer_ghana': { country: 'GH', networks: ['Bank'], currency: 'GHS' },
  'bank_transfer_kenya': { country: 'KE', networks: ['Bank'], currency: 'KES' },
  'bank_transfer_nigeria': { country: 'NG', networks: ['Bank'], currency: 'NGN' },
  'card_payment': { country: 'NG', networks: ['Visa', 'Mastercard', 'Verve'], currency: 'NGN' },
}

// Available networks based on payment type
const availableNetworks = computed(() => {
  return paymentConfigs[paymentType.value]?.networks || []
})

// Watch payment type changes to update country and reset network
watch(paymentType, newType => {
  const config = paymentConfigs[newType]
  if (config) {
    country.value = config.country
    network.value = config.networks[0] || ''
  }
})

// Form validation
const validateForm = () => {
  const errors = {}

  if (!fullname.value.trim()) errors.fullname = 'Full name is required'
  if (!email.value.trim()) errors.email = 'Email is required'
  else if (!/\S+@\S+\.\S+/.test(email.value)) errors.email = 'Email is invalid'
  if (!phone.value.trim()) errors.phone = 'Phone number is required'
  if (!amount.value || amount.value <= 0) errors.amount = 'Amount must be greater than 0'
  if (!network.value) errors.network = 'Network is required'

  formErrors.value = errors
  
  return Object.keys(errors).length === 0
}

const pay = async () => {
  if (!validateForm()) return

  isLoading.value = true

  try {
    const config = paymentConfigs[paymentType.value]

    // Format phone number for Korapay (ensure it starts with country code)
    const formatPhoneNumber = (phoneNum, countryCode) => {
      let formattedPhone = phoneNum.replace(/\D/g, '') // Remove non-digits

      if (countryCode === 'GH' && !formattedPhone.startsWith('233')) {
        formattedPhone = formattedPhone.startsWith('0')
          ? '233' + formattedPhone.substring(1)
          : '233' + formattedPhone
      } else if (countryCode === 'KE' && !formattedPhone.startsWith('254')) {
        formattedPhone = formattedPhone.startsWith('0')
          ? '254' + formattedPhone.substring(1)
          : '254' + formattedPhone
      } else if (countryCode === 'NG' && !formattedPhone.startsWith('234')) {
        formattedPhone = formattedPhone.startsWith('0')
          ? '234' + formattedPhone.substring(1)
          : '234' + formattedPhone
      }

      return formattedPhone
    }

    const formattedPhone = formatPhoneNumber(phone.value, country.value)

    const paymentData = {
      amount: parseInt(amount.value),
      currency: config?.currency || 'GHS',
      reference: txRef.value,
      notification_url: 'https://webhook.site/1c209942-1a66-4cdf-a7ab-78d9cc684ad2',
      redirect_url: 'http://localhost:5173/korapay/redirect-results',
      merchant_bears_cost: true,
      customer: {
        name: fullname.value,
        email: email.value
      },
      mobile_money_phone: formattedPhone,
      mobile_money_provider: network.value,
      payment_method: paymentType.value.includes('mobile_money') ? 'mobile_money' :
                     paymentType.value.includes('bank_transfer') ? 'bank_transfer' :
                     paymentType.value.includes('card') ? 'card' : 'mobile_money',
      mobile_money: {
        phone: formattedPhone,
        provider: network.value
      }
    }

    const res = await korapayStore.initiatePayment(paymentData)
    const paymentLink = res.data?.checkout_url || res.data?.data?.checkout_url || res.data?.link || res.data?.data?.link

    if (paymentLink) {
      router.push({
        name: 'korapay-payment-redirect',
        query: { link: encodeURIComponent(paymentLink) },
      })
    } else {
      throw new Error('Payment link not received')
    }
  } catch (e) {
    console.error('Korapay payment initiation failed:', e)
    alert('Payment failed. Please try again.')
  } finally {
    isLoading.value = false
  }
}

// Auto-generate transaction reference on mount
onMounted(() => {
  txRef.value = generateTxRef()
})

// Computed property to show the payload that will be sent
const payloadPreview = computed(() => {
  const config = paymentConfigs[paymentType.value]

  const formatPhoneNumber = (phoneNum, countryCode) => {
    let formattedPhone = phoneNum.replace(/\D/g, '')

    if (countryCode === 'GH' && !formattedPhone.startsWith('233')) {
      formattedPhone = formattedPhone.startsWith('0')
        ? '233' + formattedPhone.substring(1)
        : '233' + formattedPhone
    } else if (countryCode === 'KE' && !formattedPhone.startsWith('254')) {
      formattedPhone = formattedPhone.startsWith('0')
        ? '254' + formattedPhone.substring(1)
        : '254' + formattedPhone
    } else if (countryCode === 'NG' && !formattedPhone.startsWith('234')) {
      formattedPhone = formattedPhone.startsWith('0')
        ? '234' + formattedPhone.substring(1)
        : '234' + formattedPhone
    }

    return formattedPhone
  }

  const formattedPhone = formatPhoneNumber(phone.value, country.value)

  return {
    amount: parseInt(amount.value) || 0,
    currency: config?.currency || 'GHS',
    reference: txRef.value,
    notification_url: 'https://webhook.site/1c209942-1a66-4cdf-a7ab-78d9cc684ad2',
    redirect_url: 'http://localhost:5173/korapay/redirect-results',
    merchant_bears_cost: true,
    customer: {
      name: fullname.value,
      email: email.value
    },
    mobile_money_phone: formattedPhone,
    mobile_money_provider: network.value,
    payment_method: paymentType.value.includes('mobile_money') ? 'mobile_money' :
                   paymentType.value.includes('bank_transfer') ? 'bank_transfer' :
                   paymentType.value.includes('card') ? 'card' : 'mobile_money',
    mobile_money: {
      phone: formattedPhone,
      provider: network.value
    }
  }
})
</script>

<template>
  <div class="payment-page">
    <!-- Header Section -->
    <div class="text-center mb-8">
      <VIcon
        icon="ri-secure-payment-line"
        size="48"
        color="primary"
        class="mb-4"
      />
      <h1 class="text-h4 font-weight-bold mb-2">
        Korapay Payment Gateway
      </h1>
      <p class="text-body-1 text-medium-emphasis">
        Secure payment processing with Korapay
      </p>
    </div>

    <!-- Payment Form -->
    <VCard
      class="mx-auto payment-card"
      max-width="800"
      elevation="8"
    >
      <VCardTitle class="d-flex align-center pa-6 bg-primary">
        <VIcon
          icon="ri-wallet-3-line"
          class="me-3"
          color="white"
        />
        <span class="text-white">Korapay Payment Details</span>
      </VCardTitle>

      <VCardText class="pa-6">
        <form @submit.prevent="pay">
          <!-- Personal Information Section -->
          <div class="mb-6">
            <h3 class="text-h6 mb-4 d-flex align-center">
              <VIcon
                icon="ri-user-line"
                class="me-2"
                color="primary"
              />
              Personal Information
            </h3>
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="fullname"
                  label="Full Name"
                  prepend-inner-icon="ri-user-line"
                  variant="outlined"
                  :error-messages="formErrors.fullname"
                  required
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="email"
                  label="Email Address"
                  prepend-inner-icon="ri-mail-line"
                  variant="outlined"
                  type="email"
                  :error-messages="formErrors.email"
                  required
                />
              </VCol>
            </VRow>
          </div>

          <!-- Payment Information Section -->
          <div class="mb-6">
            <h3 class="text-h6 mb-4 d-flex align-center">
              <VIcon
                icon="ri-smartphone-line"
                class="me-2"
                color="primary"
              />
              Payment Information
            </h3>
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="paymentType"
                  label="Payment Method"
                  prepend-inner-icon="ri-bank-card-line"
                  variant="outlined"
                  :items="[
                    { value: 'mobile_money_ghana', title: 'Mobile Money (Ghana)' },
                    { value: 'mobile_money_kenya', title: 'Mobile Money (Kenya)' },
                    { value: 'mobile_money_nigeria', title: 'Mobile Money (Nigeria)' },
                    { value: 'bank_transfer_ghana', title: 'Bank Transfer (Ghana)' },
                    { value: 'bank_transfer_kenya', title: 'Bank Transfer (Kenya)' },
                    { value: 'bank_transfer_nigeria', title: 'Bank Transfer (Nigeria)' },
                    { value: 'card_payment', title: 'Card Payment' },
                  ]"
                  required
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="network"
                  label="Network Provider"
                  prepend-inner-icon="ri-signal-tower-line"
                  variant="outlined"
                  :items="availableNetworks"
                  :error-messages="formErrors.network"
                  required
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="phone"
                  label="Phone Number"
                  prepend-inner-icon="ri-phone-line"
                  variant="outlined"
                  :error-messages="formErrors.phone"
                  :placeholder="country === 'GH' ? 'e.g. **********' :
                               country === 'KE' ? 'e.g. **********' :
                               country === 'NG' ? 'e.g. 08012345678' : 'e.g. **********'"
                  :hint="`Phone number will be formatted for ${country} (+${country === 'GH' ? '233' : country === 'KE' ? '254' : country === 'NG' ? '234' : '233'})`"
                  required
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="amount"
                  label="Amount"
                  prepend-inner-icon="ri-money-dollar-circle-line"
                  variant="outlined"
                  type="number"
                  min="1"
                  :error-messages="formErrors.amount"
                  :suffix="paymentConfigs[paymentType]?.currency || 'GHS'"
                  required
                />
              </VCol>
            </VRow>
          </div>

          <!-- Transaction Details Section -->
          <div class="mb-6">
            <h3 class="text-h6 mb-4 d-flex align-center">
              <VIcon
                icon="ri-file-list-3-line"
                class="me-2"
                color="primary"
              />
              Transaction Details
            </h3>
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="country"
                  label="Country Code"
                  prepend-inner-icon="ri-global-line"
                  variant="outlined"
                  readonly
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="txRef"
                  label="Transaction Reference"
                  prepend-inner-icon="ri-hashtag"
                  variant="outlined"
                  hint="Auto-generated unique reference"
                />
              </VCol>
              <VCol cols="12">
                <VTextField
                  v-model="order_ref"
                  label="Order Reference"
                  prepend-inner-icon="ri-shopping-cart-line"
                  variant="outlined"
                  hint="Auto-generated order reference"
                />
              </VCol>
            </VRow>
          </div>

          <!-- Payload Preview Section -->
          <div class="mb-6">
            <h3 class="text-h6 mb-4 d-flex align-center">
              <VIcon
                icon="ri-code-line"
                class="me-2"
                color="info"
              />
              Payload Preview
            </h3>
            <VCard
              variant="tonal"
              color="info"
              class="mb-4"
            >
              <VCardText>
                <pre class="text-caption">{{ JSON.stringify(payloadPreview, null, 2) }}</pre>
              </VCardText>
            </VCard>
          </div>

          <!-- Submit Button -->
          <div class="text-center">
            <VBtn
              type="submit"
              color="primary"
              size="large"
              :loading="isLoading"
              :disabled="isLoading"
              class="px-8 py-3"
              elevation="2"
            >
              <VIcon
                icon="ri-secure-payment-line"
                class="me-2"
              />
              {{ isLoading ? 'Processing...' : 'Pay with Korapay' }}
            </VBtn>
          </div>
        </form>
      </VCardText>
    </VCard>

    <!-- Security Notice -->
    <VCard
      class="mx-auto mt-6 security-notice"
      max-width="800"
      variant="tonal"
      color="success"
    >
      <VCardText class="text-center pa-4">
        <VIcon
          icon="ri-shield-check-line"
          class="me-2"
          color="success"
        />
        <span class="text-success">
          Your payment is secured with Korapay's 256-bit SSL encryption
        </span>
      </VCardText>
    </VCard>
  </div>
</template>

<style scoped>
.payment-page {
  padding: 2rem 1rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.payment-card {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.payment-card .v-card-title {
  border-radius: 16px 16px 0 0 !important;
  background: linear-gradient(135deg, rgb(var(--v-theme-primary)) 0%, rgba(var(--v-theme-primary), 0.8) 100%) !important;
}

.security-notice {
  border-radius: 12px !important;
  background: rgba(var(--v-theme-on-secondary), 1) !important;
  border: 1px solid rgba(var(--v-theme-success), 0.2) !important;
}

.v-text-field--variant-outlined .v-field {
  border-radius: 12px;
}

.v-select--variant-outlined .v-field {
  border-radius: 12px;
}

.v-btn {
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
}

pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

@media (max-width: 768px) {
  .payment-page {
    padding: 1rem 0.5rem;
  }

  .payment-card {
    margin: 0 !important;
  }
}
</style>
